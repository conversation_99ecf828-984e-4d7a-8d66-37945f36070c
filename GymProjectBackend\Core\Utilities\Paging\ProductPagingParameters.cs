using System;

namespace Core.Utilities.Paging
{
    public class ProductPagingParameters : PagingParameters
    {
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public bool? IsActive { get; set; }

        public ProductPagingParameters()
        {
            PageSize = 20; // Varsayılan sayfa boyutu
            PageNumber = 1; // Varsayılan sayfa numarası
            SearchText = ""; // Boş arama metni
            SortBy = "ProductID"; // Varsayılan sıralama alanı
            SortDirection = "desc"; // Varsayılan sıralama yönü (en yeni önce)
        }
    }
}
